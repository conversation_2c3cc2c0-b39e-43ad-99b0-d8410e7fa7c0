# Voluntary Associations and Generalized Trust: Nonrecursive Models Analysis

## Files in this Analysis

### Main Analysis Script
- **`voluntary_associations_trust_models.R`**: Complete R script that replicates and extends the two models from the study
  - Model 1: Exactly identified model (Figure 2.4)
  - Model 2: Overidentified model (Figure 2.5)
  - Extensions and additional analyses

### Output Files
- **`analysis_summary.md`**: Comprehensive summary of results and findings
- **`model1_path_diagram.png`**: Path diagram for the exactly identified model
- **`model2_path_diagram.png`**: Path diagram for the overidentified model

## Data Source
- **Dataset**: `impgss7594a.dta` from the ICPSR GSS data
- **Sample**: 4,598 complete cases from GSS 1993-1994
- **Variables**: Trust, civic participation, education, and instrumental variables

## Key Results

1. **Model 1 (Exactly Identified)** shows superior fit and significant reciprocal effects
2. **Model 2 (Overidentified)** has poorer fit and non-significant reciprocal effects
3. **Strong evidence** for reciprocal relationship between trust and voluntary associations
4. **Methodological insight**: More instruments don't always improve model performance

## How to Run

1. Ensure you have R with the following packages installed:
   - `haven`, `lavaan`, `semPlot`, `dplyr`, `ggplot2`

2. Make sure the data file `../data/impgss7594a.dta` is accessible

3. Run the main script:
   ```r
   source("voluntary_associations_trust_models.R")
   ```

## Theoretical Background

This analysis is based on social capital theory, which posits that:
- Voluntary association membership increases generalized trust through social interaction
- Higher trust makes individuals more likely to join voluntary associations
- This creates a reciprocal, mutually reinforcing relationship

The study uses instrumental variables to identify this reciprocal relationship:
- **For associations**: Young children (time constraint), TV viewing (social isolation)
- **For trust**: Burglary experience, parents' divorce (trust-affecting experiences)

## Statistical Approach

- **Structural Equation Modeling (SEM)** with instrumental variables
- **Maximum Likelihood estimation**
- **Nonrecursive models** to handle reciprocal causation
- **Model comparison** using fit indices and information criteria
