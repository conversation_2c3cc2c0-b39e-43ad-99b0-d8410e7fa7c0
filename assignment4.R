# National Identity Structural Equation Model Analysis
# Load required packages
library(lavaan)
library(semPlot)

# Step 1: Create the covariance matrix
# Lower triangular values from the data
cov_matrix <- matrix(c(
  .959,    0,      0,      0,      0,      0,      0,
  .194,   .358,    0,      0,      0,      0,      0,
  .413,   .168,   .740,    0,      0,      0,      0,
  .413,   .170,   .451,   .712,    0,      0,      0,
  .144,   .122,   .122,   .118,   .398,    0,      0,
  .131,   .126,   .159,   .162,   .132,   .293,    0,
  .121,   .105,   .130,   .130,   .091,   .120,   .247
), nrow = 7, byrow = TRUE)

# Make it symmetric
cov_matrix[upper.tri(cov_matrix)] <- t(cov_matrix)[upper.tri(cov_matrix)]

# Add variable names
var_names <- c("NATRELIG", "NATCULTU", "NATBORN", "NATPAREN", 
               "NATRESPE", "NATFEEL", "NATLANG")
rownames(cov_matrix) <- colnames(cov_matrix) <- var_names

# Display the covariance matrix
print("Covariance Matrix:")
print(round(cov_matrix, 3))

# Step 2: Specify the original two-factor model
# Factor 1 (Ethnic): NATRELIG, NATCULTU, NATBORN
# Factor 2 (Civic): NATPAREN, NATRESPE, NATFEEL, NATLANG
# Scaling: Ethnic to NATBORN, Civic to NATFEEL

model1 <- '
  # Factor loadings
  ethnic =~ NATRELIG + NATCULTU + 1*NATBORN
  civic =~ NATPAREN + NATRESPE + 1*NATFEEL + NATLANG
  
  # Factor covariance
  ethnic ~~ civic
'

# Fit the original model
fit1 <- sem(model1, sample.cov = cov_matrix, sample.nobs = 809)

# Display results for Model 1
cat("\n========== ORIGINAL MODEL (Model 1) ==========\n")
summary(fit1, fit.measures = TRUE, standardized = TRUE)

# Get fit measures for Model 1
fitmeasures(fit1, c("chisq", "df", "pvalue", "cfi", "tli", "rmsea", 
                    "rmsea.ci.lower", "rmsea.ci.upper", "srmr", "gfi", "agfi"))

# Step 3: Re-specify the model based on theoretical considerations
# Adding cross-loadings based on theory:
# - NATFEEL and NATCULTU are linked (feeling nationality and cultural traditions)
# - NATFEEL and NATRESPE are linked (feeling nationality and respecting laws)

model2 <- '
  # Factor loadings with cross-loading
  ethnic =~ NATRELIG + NATCULTU + 1*NATBORN + NATFEEL
  civic =~ 1*NATPAREN + NATRESPE + NATFEEL + NATLANG
  # Factor covariance
  ethnic ~~ civic
'

# Fit the re-specified model
fit2 <- sem(model2, sample.cov = cov_matrix, sample.nobs = 809)

# Display results for Model 2
cat("\n========== RE-SPECIFIED MODEL (Model 2) ==========\n")
summary(fit2, fit.measures = TRUE, standardized = TRUE)

# Get fit measures for Model 2
fitmeasures(fit2, c("chisq", "df", "pvalue", "cfi", "tli", "rmsea", 
                    "rmsea.ci.lower", "rmsea.ci.upper", "srmr", "gfi", "agfi"))

# Step 4: Model comparison
cat("\n========== MODEL COMPARISON ==========\n")
anova(fit1, fit2)

# Step 5: Check modification indices for Model 1
cat("\n========== MODIFICATION INDICES FOR MODEL 1 ==========\n")
mi1 <- modificationindices(fit1, sort = TRUE)
print(mi1[mi1$mi > 3.84, ])  # Show only significant MIs

# Step 6: Alternative re-specification based on modification indices
# Let's try allowing residual covariances instead of cross-loadings

model3 <- '
  # Factor loadings (using reference indicators like Models 1 & 2)
  ethnic =~ NATRELIG + NATCULTU + 1*NATBORN
  civic =~ NATPAREN + NATRESPE + 1*NATFEEL + NATLANG

  # Factor covariance
  ethnic ~~ civic

  # Add residual covariances 
  NATFEEL ~~ NATCULTU  # Feeling and culture link
  NATFEEL ~~ NATRESPE  # Feeling and respect link
'

# Fit the alternative model
fit3 <- sem(model3, sample.cov = cov_matrix, sample.nobs = 809)

# Display results for Model 3
cat("\n========== ALTERNATIVE MODEL (Model 3) ==========\n")
summary(fit3, fit.measures = TRUE, standardized = TRUE)

# Get fit measures for Model 3
fitmeasures(fit3, c("chisq", "df", "pvalue", "cfi", "tli", "rmsea", 
                    "rmsea.ci.lower", "rmsea.ci.upper", "srmr", "gfi", "agfi"))

# Step 7: Additional model with cross-loading for NATCULTU
model4 <- '
  # Factor loadings with additional cross-loading
  ethnic =~ NATRELIG + NATCULTU + 1*NATBORN
  civic =~ NATPAREN + NATRESPE + 1*NATFEEL + NATLANG + NATCULTU

  # Factor covariance
  ethnic ~~ civic
'

# Fit the additional model
fit4 <- sem(model4, sample.cov = cov_matrix, sample.nobs = 809)

# Display results for Model 4
cat("\n========== ADDITIONAL MODEL (Model 4) ==========\n")
summary(fit4, fit.measures = TRUE, standardized = TRUE)

# Get fit measures for Model 4
fitmeasures(fit4, c("chisq", "df", "pvalue", "cfi", "tli", "rmsea",
                    "rmsea.ci.lower", "rmsea.ci.upper", "srmr", "gfi", "agfi"))

# Compare all four models
cat("\n========== COMPARISON OF ALL MODELS ==========\n")
fit_comparison <- data.frame(
  Model = c("Model 1 (Original)", "Model 2 (Cross-loading)",
            "Model 3 (Residual Cov)", "Model 4 (NATCULTU Cross)"),
  ChiSq = c(fitMeasures(fit1, "chisq"),
            fitMeasures(fit2, "chisq"),
            fitMeasures(fit3, "chisq"),
            fitMeasures(fit4, "chisq")),
  df = c(fitMeasures(fit1, "df"),
         fitMeasures(fit2, "df"),
         fitMeasures(fit3, "df"),
         fitMeasures(fit4, "df")),
  pvalue = c(fitMeasures(fit1, "pvalue"),
             fitMeasures(fit2, "pvalue"),
             fitMeasures(fit3, "pvalue"),
             fitMeasures(fit4, "pvalue")),
  CFI = c(fitMeasures(fit1, "cfi"),
          fitMeasures(fit2, "cfi"),
          fitMeasures(fit3, "cfi"),
          fitMeasures(fit4, "cfi")),
  TLI = c(fitMeasures(fit1, "tli"),
          fitMeasures(fit2, "tli"),
          fitMeasures(fit3, "tli"),
          fitMeasures(fit4, "tli")),
  RMSEA = c(fitMeasures(fit1, "rmsea"),
            fitMeasures(fit2, "rmsea"),
            fitMeasures(fit3, "rmsea"),
            fitMeasures(fit4, "rmsea")),
  SRMR = c(fitMeasures(fit1, "srmr"),
           fitMeasures(fit2, "srmr"),
           fitMeasures(fit3, "srmr"),
           fitMeasures(fit4, "srmr"))
)
# Round only numeric columns, keep Model names as is
fit_comparison_rounded <- fit_comparison
fit_comparison_rounded[, -1] <- round(fit_comparison[, -1], 4)
print(fit_comparison_rounded)

# Plot the best fitting model
cat("\n========== PLOTTING BEST MODEL ==========\n")
# Based on fit indices, Model 2 (Cross-loading) has the best fit
cat("Best fitting model: Model 2 (Cross-loading)\n")
cat("- Lowest Chi-square:", round(fitMeasures(fit2, "chisq"), 3), "\n")
cat("- Highest CFI:", round(fitMeasures(fit2, "cfi"), 3), "\n")
cat("- Lowest RMSEA:", round(fitMeasures(fit2, "rmsea"), 3), "\n")

# Create path diagram for the best model (Model 2)
if(require(semPlot, quietly = TRUE)) {
  # Add title manually
  cat("Creating path diagram for Model 2: Cross-loading Model (Best Fit)\n")
  semPaths(fit2, "std",
           edge.label.cex = 0.8,
           curvePivot = TRUE,
           layout = "tree2",
           rotation = 2,
           edge.color = "black",
           node.color = "lightblue")
} else {
  cat("semPlot package not available. Install with: install.packages('semPlot')\n")
}

# Summary of best model
cat("\n========== BEST MODEL SUMMARY ==========\n")
cat("Model 2 allows NATFEEL to load on both ethnic and civic factors,\n")
cat("reflecting the theoretical connection between feeling nationality\n")
cat("and both ethnic and civic dimensions of national identity.\n")